#!/usr/bin/env python3
"""
Simple Async Test - Verbose Output
"""

import asyncio
import aiohttp
import time
import sys

async def single_request(session, request_id, base_url):
    print(f"🚀 Request {request_id}: Starting...")
    sys.stdout.flush()
    
    start_time = time.time()
    
    try:
        data = aiohttp.FormData()
        
        print(f"📁 Request {request_id}: Reading audio files...")
        sys.stdout.flush()

        # Read files into memory first
        with open("Deepak paudel.mp3", 'rb') as f:
            source_data = f.read()

        with open("canadian_cheerful.mp3", 'rb') as f:
            ref_data = f.read()

        # Add to form data
        data.add_field('source_audio', source_data, filename="Deepak paudel.mp3", content_type='audio/mpeg')
        data.add_field('reference_audio', ref_data, filename="canadian_cheerful.mp3", content_type='audio/mpeg')
        
        data.add_field('noise_cancellation', 'false')
        data.add_field('normalize_audio_flag', 'false')
        data.add_field('flow_matching_steps', '20')  # Optimized: reduced from 32
        
        print(f"📤 Request {request_id}: Sending to API...")
        sys.stdout.flush()
        
        timeout = aiohttp.ClientTimeout(total=300)
        async with session.post(f"{base_url}/timbre-transfer/", data=data, timeout=timeout) as response:
            print(f"📥 Request {request_id}: Got response {response.status}")
            sys.stdout.flush()
            
            if response.status == 200:
                content = await response.read()
                end_time = time.time()
                latency = end_time - start_time
                
                output_file = f"output_{request_id}.wav"
                with open(output_file, 'wb') as f:
                    f.write(content)
                
                print(f"✅ Request {request_id}: SUCCESS in {latency:.2f}s")
                sys.stdout.flush()
                return {"id": request_id, "success": True, "latency": latency}
            else:
                print(f"❌ Request {request_id}: FAILED {response.status}")
                sys.stdout.flush()
                return {"id": request_id, "success": False, "error": response.status}
                
    except Exception as e:
        print(f"💥 Request {request_id}: ERROR {e}")
        sys.stdout.flush()
        return {"id": request_id, "success": False, "error": str(e)}

async def main():
    base_url = "http://localhost:8011"
    num_requests = 3
    
    print(f"🎵 Testing {num_requests} concurrent requests")
    print(f"🌐 URL: {base_url}")
    sys.stdout.flush()
    
    start_time = time.time()
    
    connector = aiohttp.TCPConnector(limit=10)
    async with aiohttp.ClientSession(connector=connector) as session:
        tasks = [single_request(session, i, base_url) for i in range(1, num_requests + 1)]
        results = await asyncio.gather(*tasks)
    
    end_time = time.time()
    total_time = end_time - start_time
    
    print(f"\n⏱️ Total time: {total_time:.2f}s")
    
    successful = [r for r in results if r.get('success')]
    failed = [r for r in results if not r.get('success')]
    
    print(f"✅ Successful: {len(successful)}")
    print(f"❌ Failed: {len(failed)}")
    
    if successful:
        latencies = [r['latency'] for r in successful]
        print(f"📊 Avg latency: {sum(latencies)/len(latencies):.2f}s")
        print(f"📊 Min latency: {min(latencies):.2f}s")
        print(f"📊 Max latency: {max(latencies):.2f}s")

if __name__ == "__main__":
    asyncio.run(main())
