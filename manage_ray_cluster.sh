#!/bin/bash

# Ray Cluster Management Script for Distributed Timbre Transfer
# This script helps manage the Ray cluster setup across multiple servers

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
HEAD_IP="*************"  # Current server IP (head node)
WORKER_IP="*************"  # Remote server IP (worker node)
RAY_PORT="10001"
DASHBOARD_PORT="8265"
SERVICE_PORT="8011"
GPU_IDS="1,2,3"

# Function to print colored output
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if virtual environment exists
check_venv() {
    if [ -f ".venv/bin/activate" ]; then
        print_info "Activating virtual environment..."
        source .venv/bin/activate
        print_success "Virtual environment activated"
    else
        print_warning "Virtual environment not found at .venv/bin/activate"
    fi
}

# Function to start head node
start_head_node() {
    print_info "🚀 Starting Ray head node on $HEAD_IP..."
    
    check_venv
    
    print_info "Head node configuration:"
    print_info "  - IP: $HEAD_IP"
    print_info "  - Ray port: $RAY_PORT"
    print_info "  - Dashboard: http://$HEAD_IP:$DASHBOARD_PORT"
    print_info "  - Service: http://$HEAD_IP:$SERVICE_PORT"
    print_info "  - GPUs: $GPU_IDS"
    
    # Set CUDA_VISIBLE_DEVICES
    export CUDA_VISIBLE_DEVICES=$GPU_IDS
    
    # Start head node
    python3 start_ray_head.py \
        --head-ip "$HEAD_IP" \
        --port "$RAY_PORT" \
        --dashboard-port "$DASHBOARD_PORT" \
        --num-gpus 3
}

# Function to show worker connection command
show_worker_command() {
    print_info "🔗 To connect the worker node ($WORKER_IP), run this command on the remote server:"
    echo ""
    echo "# On server $WORKER_IP:"
    echo "cd /path/to/Timbre-Transfer"
    echo "source .venv/bin/activate"
    echo "export CUDA_VISIBLE_DEVICES=$GPU_IDS"
    echo "python3 start_ray_worker.py --head-ip $HEAD_IP --head-port $RAY_PORT --num-gpus 3 --gpu-ids $GPU_IDS"
    echo ""
    print_info "Or use the automated script:"
    echo "bash manage_ray_cluster.sh connect-worker"
}

# Function to connect worker (to be run on remote server)
connect_worker() {
    print_info "🔗 Connecting worker node to head node at $HEAD_IP:$RAY_PORT..."
    
    check_venv
    
    # Set CUDA_VISIBLE_DEVICES
    export CUDA_VISIBLE_DEVICES=$GPU_IDS
    
    # Connect worker
    python3 start_ray_worker.py \
        --head-ip "$HEAD_IP" \
        --head-port "$RAY_PORT" \
        --num-gpus 3 \
        --gpu-ids "$GPU_IDS"
}

# Function to check cluster status
check_cluster_status() {
    print_info "📊 Checking Ray cluster status..."
    
    check_venv
    
    python3 -c "
import ray
import requests
import json

try:
    # Connect to existing cluster
    ray.init(address='$HEAD_IP:$RAY_PORT', ignore_reinit_error=True)
    
    # Get cluster resources
    resources = ray.cluster_resources()
    nodes = ray.nodes()
    
    print('🌐 Ray Cluster Status:')
    print(f'  Total GPUs: {resources.get(\"GPU\", 0)}')
    print(f'  Total CPUs: {resources.get(\"CPU\", 0)}')
    print(f'  Alive nodes: {len([n for n in nodes if n[\"Alive\"]])}')
    print()
    
    # Show detailed node information
    for i, node in enumerate(nodes):
        if node['Alive']:
            node_ip = node['NodeManagerAddress']
            node_resources = node['Resources']
            node_gpus = node_resources.get('GPU', 0)
            node_cpus = node_resources.get('CPU', 0)
            node_type = 'Head' if i == 0 else 'Worker'
            print(f'  {node_type} Node: {node_ip} - {node_gpus} GPUs, {node_cpus} CPUs')
    
    ray.shutdown()
    
    # Check service status
    print()
    print('🎵 Timbre Transfer Service Status:')
    try:
        response = requests.get(f'http://$HEAD_IP:$SERVICE_PORT/health', timeout=5)
        if response.status_code == 200:
            health_data = response.json()
            print(f'  Status: {health_data.get(\"status\", \"unknown\")}')
            print(f'  Ray status: {health_data.get(\"ray_status\", \"unknown\")}')
            if 'gpu_memory' in health_data:
                gpu_mem = health_data['gpu_memory']
                print(f'  GPU memory: {gpu_mem.get(\"allocated_gb\", 0):.1f}GB allocated, {gpu_mem.get(\"reserved_gb\", 0):.1f}GB reserved')
        else:
            print(f'  Service not responding (HTTP {response.status_code})')
    except Exception as e:
        print(f'  Service not reachable: {e}')
        
except Exception as e:
    print(f'❌ Error connecting to cluster: {e}')
    print('Make sure the head node is running.')
"
}

# Function to test the distributed service
test_service() {
    print_info "🧪 Testing distributed timbre transfer service..."
    
    # Check if test files exist
    if [ ! -f "test_audio/source.wav" ] || [ ! -f "test_audio/reference.wav" ]; then
        print_warning "Test audio files not found. Creating dummy test files..."
        mkdir -p test_audio
        
        # Create dummy audio files using Python
        python3 -c "
import numpy as np
import soundfile as sf
import os

# Create test directory
os.makedirs('test_audio', exist_ok=True)

# Generate dummy audio (1 second of sine wave)
sample_rate = 24000
duration = 1.0
t = np.linspace(0, duration, int(sample_rate * duration))

# Source: 440Hz sine wave
source_audio = 0.3 * np.sin(2 * np.pi * 440 * t)
sf.write('test_audio/source.wav', source_audio, sample_rate)

# Reference: 880Hz sine wave  
ref_audio = 0.3 * np.sin(2 * np.pi * 880 * t)
sf.write('test_audio/reference.wav', ref_audio, sample_rate)

print('✅ Test audio files created')
"
    fi
    
    # Test the service
    print_info "Sending test request to distributed service..."
    
    curl -X POST "http://$HEAD_IP:$SERVICE_PORT/timbre-transfer/" \
        -F "source_audio=@test_audio/source.wav" \
        -F "reference_audio=@test_audio/reference.wav" \
        -F "noise_cancellation=false" \
        -F "normalize_audio_flag=false" \
        -F "flow_matching_steps=10" \
        --output test_audio/distributed_output.wav \
        --write-out "HTTP Status: %{http_code}\nTotal Time: %{time_total}s\n"
    
    if [ $? -eq 0 ]; then
        print_success "✅ Distributed service test completed successfully!"
        print_info "Output saved to: test_audio/distributed_output.wav"
    else
        print_error "❌ Distributed service test failed"
    fi
}

# Function to stop cluster
stop_cluster() {
    print_info "🛑 Stopping Ray cluster..."
    
    check_venv
    
    # Try to shutdown gracefully
    python3 -c "
import ray
try:
    ray.init(address='$HEAD_IP:$RAY_PORT', ignore_reinit_error=True)
    ray.shutdown()
    print('✅ Ray cluster shutdown completed')
except:
    print('⚠️ Could not connect to cluster (may already be stopped)')
" || true
    
    # Kill any remaining Ray processes
    pkill -f "ray start" || true
    pkill -f "start_ray_head.py" || true
    pkill -f "start_ray_worker.py" || true
    
    print_success "Cluster shutdown completed"
}

# Function to show usage
show_usage() {
    echo "Ray Cluster Management for Distributed Timbre Transfer"
    echo ""
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  start-head          Start Ray head node (run on $HEAD_IP)"
    echo "  connect-worker      Connect worker node (run on $WORKER_IP)"
    echo "  show-worker-cmd     Show command to connect worker node"
    echo "  status              Check cluster and service status"
    echo "  test                Test the distributed service"
    echo "  stop                Stop the Ray cluster"
    echo "  help                Show this help message"
    echo ""
    echo "Configuration:"
    echo "  Head node IP:       $HEAD_IP"
    echo "  Worker node IP:     $WORKER_IP"
    echo "  Ray port:           $RAY_PORT"
    echo "  Dashboard:          http://$HEAD_IP:$DASHBOARD_PORT"
    echo "  Service:            http://$HEAD_IP:$SERVICE_PORT"
    echo "  GPU IDs:            $GPU_IDS"
}

# Main execution
case "${1:-help}" in
    start-head)
        start_head_node
        ;;
    connect-worker)
        connect_worker
        ;;
    show-worker-cmd)
        show_worker_command
        ;;
    status)
        check_cluster_status
        ;;
    test)
        test_service
        ;;
    stop)
        stop_cluster
        ;;
    help|--help|-h)
        show_usage
        ;;
    *)
        print_error "Unknown command: $1"
        show_usage
        exit 1
        ;;
esac
