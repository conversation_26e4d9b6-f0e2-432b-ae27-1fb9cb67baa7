#!/usr/bin/env python3
"""
Batch Test Script for Ray Serve Timbre Transfer API

Tests different scenarios to measure the API's concurrent handling capabilities.
"""

import asyncio
import subprocess
import time
import json
import os
from typing import List, Dict

class BatchTester:
    """Batch tester for different scenarios"""
    
    def __init__(self, base_url: str = "http://localhost:8011"):
        self.base_url = base_url
        self.test_scenarios = [
            {"name": "Single Request", "requests": 1, "concurrent": 1},
            {"name": "Low Load", "requests": 3, "concurrent": 3},
            {"name": "Medium Load", "requests": 6, "concurrent": 6},
            {"name": "High Load", "requests": 9, "concurrent": 9},
            {"name": "Overload Test", "requests": 12, "concurrent": 12},
            {"name": "Sequential Test", "requests": 6, "concurrent": 1},
            {"name": "Burst Test", "requests": 15, "concurrent": 15},
        ]
    
    def run_test_scenario(self, scenario: Dict) -> Dict:
        """Run a single test scenario"""
        print(f"\n{'='*60}")
        print(f"🧪 Running Test: {scenario['name']}")
        print(f"📊 Requests: {scenario['requests']}, Concurrent: {scenario['concurrent']}")
        print(f"{'='*60}")
        
        start_time = time.time()
        
        # Run the load test
        cmd = [
            "python3", "test_ray_serve_api.py",
            "--url", self.base_url,
            "--requests", str(scenario['requests']),
            "--concurrent", str(scenario['concurrent'])
        ]
        
        try:
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=600  # 10 minutes timeout
            )
            
            end_time = time.time()
            total_time = end_time - start_time
            
            return {
                "scenario": scenario,
                "success": result.returncode == 0,
                "total_time": total_time,
                "stdout": result.stdout,
                "stderr": result.stderr,
                "return_code": result.returncode
            }
            
        except subprocess.TimeoutExpired:
            return {
                "scenario": scenario,
                "success": False,
                "total_time": 600,
                "stdout": "",
                "stderr": "Test timed out after 10 minutes",
                "return_code": -1
            }
        except Exception as e:
            return {
                "scenario": scenario,
                "success": False,
                "total_time": 0,
                "stdout": "",
                "stderr": str(e),
                "return_code": -1
            }
    
    def run_all_tests(self) -> List[Dict]:
        """Run all test scenarios"""
        print(f"🚀 Starting Batch Test Suite")
        print(f"🌐 API URL: {self.base_url}")
        print(f"📋 Total scenarios: {len(self.test_scenarios)}")
        
        results = []
        
        for i, scenario in enumerate(self.test_scenarios, 1):
            print(f"\n🔄 Progress: {i}/{len(self.test_scenarios)}")
            
            # Wait between tests to let the system recover
            if i > 1:
                print("⏳ Waiting 30 seconds between tests...")
                time.sleep(30)
            
            result = self.run_test_scenario(scenario)
            results.append(result)
            
            # Print immediate result
            if result['success']:
                print(f"✅ {scenario['name']} completed in {result['total_time']:.2f}s")
            else:
                print(f"❌ {scenario['name']} failed: {result['stderr']}")
        
        return results
    
    def generate_report(self, results: List[Dict]) -> str:
        """Generate a comprehensive test report"""
        report = []
        report.append("# Ray Serve Timbre Transfer API - Batch Test Report")
        report.append(f"Generated: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        report.append(f"API URL: {self.base_url}")
        report.append("")
        
        # Summary
        successful_tests = [r for r in results if r['success']]
        failed_tests = [r for r in results if not r['success']]
        
        report.append("## Summary")
        report.append(f"- Total Tests: {len(results)}")
        report.append(f"- Successful: {len(successful_tests)}")
        report.append(f"- Failed: {len(failed_tests)}")
        report.append(f"- Success Rate: {len(successful_tests)/len(results)*100:.1f}%")
        report.append("")
        
        # Detailed Results
        report.append("## Detailed Results")
        report.append("")
        
        for result in results:
            scenario = result['scenario']
            status = "✅ PASSED" if result['success'] else "❌ FAILED"
            
            report.append(f"### {scenario['name']} - {status}")
            report.append(f"- Requests: {scenario['requests']}")
            report.append(f"- Concurrent: {scenario['concurrent']}")
            report.append(f"- Total Time: {result['total_time']:.2f}s")
            
            if result['success']:
                # Extract key metrics from stdout
                stdout_lines = result['stdout'].split('\n')
                for line in stdout_lines:
                    if 'Success Rate:' in line or 'Mean:' in line or 'Requests/second:' in line:
                        report.append(f"- {line.strip()}")
            else:
                report.append(f"- Error: {result['stderr']}")
            
            report.append("")
        
        # Performance Analysis
        if successful_tests:
            report.append("## Performance Analysis")
            
            # Find best performing test
            best_test = min(successful_tests, key=lambda x: x['total_time'])
            worst_test = max(successful_tests, key=lambda x: x['total_time'])
            
            report.append(f"- Fastest Test: {best_test['scenario']['name']} ({best_test['total_time']:.2f}s)")
            report.append(f"- Slowest Test: {worst_test['scenario']['name']} ({worst_test['total_time']:.2f}s)")
            
            # Concurrent capacity analysis
            concurrent_tests = [r for r in successful_tests if r['scenario']['concurrent'] > 1]
            if concurrent_tests:
                max_concurrent = max(concurrent_tests, key=lambda x: x['scenario']['concurrent'])
                report.append(f"- Max Successful Concurrent: {max_concurrent['scenario']['concurrent']} requests")
            
            report.append("")
        
        # Recommendations
        report.append("## Recommendations")
        if len(successful_tests) == len(results):
            report.append("- ✅ All tests passed! The API is handling load well.")
        elif len(successful_tests) > len(results) * 0.8:
            report.append("- ⚠️ Most tests passed, but some high-load scenarios failed.")
            report.append("- Consider optimizing for higher concurrent loads.")
        else:
            report.append("- ❌ Multiple test failures detected.")
            report.append("- Review API configuration and resource allocation.")
        
        return '\n'.join(report)
    
    def save_report(self, results: List[Dict], filename: str = None):
        """Save test report to file"""
        if filename is None:
            timestamp = time.strftime('%Y%m%d_%H%M%S')
            filename = f"ray_serve_test_report_{timestamp}.md"
        
        report = self.generate_report(results)
        
        with open(filename, 'w') as f:
            f.write(report)
        
        print(f"📄 Test report saved: {filename}")
        return filename

def main():
    import argparse
    
    parser = argparse.ArgumentParser(description="Batch test for Ray Serve Timbre Transfer API")
    parser.add_argument("--url", default="http://localhost:8011", help="API base URL")
    parser.add_argument("--quick", action="store_true", help="Run only quick tests")
    parser.add_argument("--report", help="Custom report filename")
    
    args = parser.parse_args()
    
    # Create tester
    tester = BatchTester(args.url)
    
    # Modify scenarios for quick test
    if args.quick:
        tester.test_scenarios = [
            {"name": "Single Request", "requests": 1, "concurrent": 1},
            {"name": "Low Load", "requests": 3, "concurrent": 3},
            {"name": "Medium Load", "requests": 6, "concurrent": 6},
        ]
        print("🏃 Running quick test suite (3 scenarios)")
    
    # Run tests
    results = tester.run_all_tests()
    
    # Generate and save report
    report_file = tester.save_report(results, args.report)
    
    # Print summary
    successful = len([r for r in results if r['success']])
    total = len(results)
    
    print(f"\n{'='*60}")
    print(f"🏁 Batch Test Complete!")
    print(f"📊 Results: {successful}/{total} tests passed")
    print(f"📄 Report: {report_file}")
    print(f"{'='*60}")
    
    return 0 if successful == total else 1

if __name__ == "__main__":
    import sys
    sys.exit(main())
