# Distributed Ray Cluster Setup for Timbre Transfer

This guide explains how to set up a distributed Ray cluster for the Timbre Transfer API across multiple servers.

## 🌐 **Cluster Architecture**

```
┌─────────────────────────────────────────────────────────────────┐
│                    Ray Cluster Architecture                     │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  Head Node (*************)          Worker Node (*************) │
│  ┌─────────────────────────┐        ┌─────────────────────────┐  │
│  │ Ray Head + Dashboard    │◄──────►│ Ray Worker              │  │
│  │ Timbre Service (8011)   │        │                         │  │
│  │ GPUs: 1, 2, 3          │        │ GPUs: 1, 2, 3           │  │
│  │ Replicas: 6             │        │ Replicas: 6             │  │
│  └─────────────────────────┘        └─────────────────────────┘  │
│                                                                 │
│  Total Capacity: 12 replicas × 3 requests = 36 concurrent      │
│  Total GPUs: 6 (3 local + 3 remote)                           │
│  Processing Time: ~10 seconds for 36 requests                  │
└─────────────────────────────────────────────────────────────────┘
```

## 📋 **Prerequisites**

### **Both Servers Must Have:**
1. **Same Python environment** with all dependencies installed
2. **Same codebase** (Timbre-Transfer repository)
3. **CUDA-capable GPUs** (GPUs 1, 2, 3 available)
4. **Network connectivity** between servers
5. **<PERSON> and <PERSON>y<PERSON>or<PERSON>** installed

### **Network Requirements:**
- Port 10001: Ray cluster communication
- Port 8265: Ray dashboard
- Port 8011: Timbre Transfer API
- Firewall rules allowing communication between servers

## 🚀 **Setup Instructions**

### **Step 1: Prepare Remote Server (*************)**

1. **Copy the codebase to the remote server:**
   ```bash
   # On remote server
   git clone <repository-url> /path/to/Timbre-Transfer
   cd /path/to/Timbre-Transfer
   
   # Or copy from local server
   scp -r /root/ai_compute/Timbre-Transfer user@*************:/path/to/
   ```

2. **Set up Python environment:**
   ```bash
   # On remote server
   cd /path/to/Timbre-Transfer
   python3 -m venv .venv
   source .venv/bin/activate
   pip install -r requirements.txt
   ```

3. **Copy the new cluster scripts:**
   ```bash
   # Copy these files to remote server:
   # - start_ray_worker.py
   # - manage_ray_cluster.sh
   ```

### **Step 2: Start Head Node (*************)**

On your current server, start the Ray head node:

```bash
# Method 1: Using the management script
bash manage_ray_cluster.sh start-head

# Method 2: Direct command
python3 start_ray_head.py --head-ip ************* --port 10001 --num-gpus 3
```

**Expected Output:**
```
🚀 Starting Ray head node...
   Head IP: *************
   Port: 10001
   Dashboard: http://*************:8265
   GPUs: 3
✅ Ray head node started successfully
🚀 Deploying timbre transfer service to Ray cluster...
✅ Timbre transfer service deployed successfully
🌐 Service available at: http://0.0.0.0:8011/timbre-transfer/
🔗 Worker nodes can connect using:
   ray start --address='*************:10001' --num-gpus=3
```

### **Step 3: Connect Worker Node (*************)**

On the remote server, connect as a worker:

```bash
# Method 1: Using the management script
bash manage_ray_cluster.sh connect-worker

# Method 2: Direct command
python3 start_ray_worker.py --head-ip ************* --head-port 10001 --num-gpus 3 --gpu-ids 1,2,3
```

**Expected Output:**
```
🚀 Starting Ray worker node...
   Head node: *************:10001
   GPUs: 3 (IDs: 1,2,3)
✅ Ray worker node connected successfully
📊 Cluster resources: {'GPU': 6.0, 'CPU': 32.0, ...}
🔧 This worker contributes: 3 GPUs, 16 CPUs
```

### **Step 4: Verify Cluster Status**

Check that both nodes are connected:

```bash
# On head node (*************)
bash manage_ray_cluster.sh status
```

**Expected Output:**
```
🌐 Ray Cluster Status:
  Total GPUs: 6
  Total CPUs: 32
  Alive nodes: 2

  Head Node: ************* - 3 GPUs, 16 CPUs
  Worker Node: ************* - 3 GPUs, 16 CPUs

🎵 Timbre Transfer Service Status:
  Status: healthy
  Ray status: connected
  GPU memory: 2.1GB allocated, 4.5GB reserved
```

## 🧪 **Testing the Distributed Setup**

### **Test 1: Basic Service Test**
```bash
bash manage_ray_cluster.sh test
```

### **Test 2: High Concurrency Test**
```bash
# Test with multiple concurrent requests
for i in {1..10}; do
  curl -X POST "http://*************:8011/timbre-transfer/" \
    -F "source_audio=@test_audio/source.wav" \
    -F "reference_audio=@test_audio/reference.wav" \
    -F "flow_matching_steps=10" \
    --output "test_output_$i.wav" &
done
wait
```

### **Test 3: Monitor GPU Usage**
```bash
# On both servers, monitor GPU usage
watch -n 2 nvidia-smi

# Check service stats
curl http://*************:8011/stats
```

## 📊 **Performance Expectations**

### **Distributed Cluster Capacity:**
- **Total Replicas:** 12 (6 per server)
- **Concurrent Requests:** 36 (12 replicas × 3 requests each)
- **Total GPUs:** 6 (3 local + 3 remote)
- **Processing Time:** ~10 seconds for 36 requests
- **Throughput:** ~3.6 requests/second

### **100 Concurrent Requests Timeline:**
- **Batch 1 (0-10s):** Requests 1-36 processing
- **Batch 2 (10-20s):** Requests 37-72 processing  
- **Batch 3 (20-30s):** Requests 73-100 processing
- **Total Time:** ~30 seconds (vs 42s single server)

## 🔧 **Management Commands**

```bash
# Start head node
bash manage_ray_cluster.sh start-head

# Show worker connection command
bash manage_ray_cluster.sh show-worker-cmd

# Check cluster status
bash manage_ray_cluster.sh status

# Test distributed service
bash manage_ray_cluster.sh test

# Stop cluster
bash manage_ray_cluster.sh stop
```

## 🌐 **Access Points**

- **Timbre Transfer API:** http://*************:8011/timbre-transfer/
- **Ray Dashboard:** http://*************:8265
- **Health Check:** http://*************:8011/health
- **Stats:** http://*************:8011/stats
- **Memory Cleanup:** http://*************:8011/cleanup-memory

## 🔍 **Troubleshooting**

### **Worker Connection Issues:**
1. Check network connectivity: `ping *************`
2. Verify ports are open: `telnet ************* 10001`
3. Check firewall rules
4. Ensure head node is running

### **GPU Not Detected:**
1. Check CUDA_VISIBLE_DEVICES: `echo $CUDA_VISIBLE_DEVICES`
2. Verify GPU availability: `nvidia-smi`
3. Check PyTorch CUDA: `python -c "import torch; print(torch.cuda.is_available())"`

### **Service Not Responding:**
1. Check Ray cluster status: `bash manage_ray_cluster.sh status`
2. Restart head node if needed
3. Check logs for errors

### **Memory Issues:**
1. Monitor GPU memory: `nvidia-smi`
2. Use cleanup endpoint: `curl -X POST http://*************:8011/cleanup-memory`
3. Restart workers if memory leaks persist

## 🛑 **Shutdown Procedure**

1. **Stop worker nodes first:**
   ```bash
   # On worker server (*************)
   Ctrl+C  # Stop worker process
   ```

2. **Stop head node:**
   ```bash
   # On head server (*************)
   bash manage_ray_cluster.sh stop
   ```

This distributed setup will significantly improve your capacity to handle concurrent requests while maintaining the same performance optimizations!
