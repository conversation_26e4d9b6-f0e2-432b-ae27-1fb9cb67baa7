#!/usr/bin/env python3
"""
Quick Test Script for Ray Serve Timbre Transfer API

Simple script to test the API with a single request or a few concurrent requests.
"""

import requests
import time
import json
import argparse

def test_health(base_url):
    """Test API health"""
    try:
        response = requests.get(f"{base_url}/health", timeout=10)
        if response.status_code == 200:
            health_data = response.json()
            print(f"✅ API Health: {json.dumps(health_data, indent=2)}")
            return True
        else:
            print(f"❌ Health check failed: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Health check error: {e}")
        return False

def test_stats(base_url):
    """Get API statistics"""
    try:
        response = requests.get(f"{base_url}/stats", timeout=10)
        if response.status_code == 200:
            stats = response.json()
            print(f"📊 API Stats: {json.dumps(stats, indent=2)}")
            return stats
        else:
            print(f"❌ Stats failed: HTTP {response.status_code}")
            return None
    except Exception as e:
        print(f"❌ Stats error: {e}")
        return None

def test_single_request(base_url, source_audio="Deepak paudel.mp3", reference_audio="canadian_cheerful.mp3"):
    """Test a single timbre transfer request"""
    print(f"\n🎵 Testing single timbre transfer request...")
    print(f"📁 Source: {source_audio}")
    print(f"📁 Reference: {reference_audio}")
    
    start_time = time.time()
    
    try:
        # Prepare files and data
        files = {
            'source_audio': open(source_audio, 'rb'),
            'reference_audio': open(reference_audio, 'rb')
        }
        
        data = {
            'noise_cancellation': 'false',  # Default
            'normalize_audio_flag': 'false',  # Default
            'flow_matching_steps': '20'  # Optimized: reduced from 32
        }
        
        # Send request
        response = requests.post(
            f"{base_url}/timbre-transfer/",
            files=files,
            data=data,
            timeout=300  # 5 minutes
        )
        
        end_time = time.time()
        latency = end_time - start_time
        
        # Close files
        files['source_audio'].close()
        files['reference_audio'].close()
        
        if response.status_code == 200:
            # Save output
            output_filename = f"output_test_{int(time.time())}.wav"
            with open(output_filename, 'wb') as f:
                f.write(response.content)
            
            server_latency = response.headers.get('X-Inference-Latency', 'N/A')
            response_size = len(response.content) / (1024 * 1024)  # MB
            
            print(f"✅ Request successful!")
            print(f"⏱️ Total latency: {latency:.2f} seconds")
            print(f"🖥️ Server latency: {server_latency} seconds")
            print(f"📦 Response size: {response_size:.2f} MB")
            print(f"💾 Output saved: {output_filename}")
            
            return True
        else:
            print(f"❌ Request failed: HTTP {response.status_code}")
            print(f"Error: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        print(f"❌ Request timed out after {time.time() - start_time:.2f} seconds")
        return False
    except Exception as e:
        print(f"❌ Request error: {e}")
        return False

def main():
    parser = argparse.ArgumentParser(description="Quick test for Ray Serve Timbre Transfer API")
    parser.add_argument("--url", default="http://localhost:8011", help="API base URL")
    parser.add_argument("--source", default="Deepak paudel.mp3", help="Source audio file")
    parser.add_argument("--reference", default="canadian_cheerful.mp3", help="Reference audio file")
    parser.add_argument("--health-only", action="store_true", help="Only check health")
    parser.add_argument("--stats-only", action="store_true", help="Only get stats")
    
    args = parser.parse_args()
    
    print(f"🎵 Quick Test for Ray Serve Timbre Transfer API")
    print(f"🌐 API URL: {args.url}")
    
    # Health check
    if not test_health(args.url):
        print("❌ API is not healthy. Exiting.")
        return 1
    
    if args.health_only:
        return 0
    
    # Stats check
    test_stats(args.url)
    
    if args.stats_only:
        return 0
    
    # Single request test
    success = test_single_request(args.url, args.source, args.reference)
    
    return 0 if success else 1

if __name__ == "__main__":
    import sys
    sys.exit(main())
