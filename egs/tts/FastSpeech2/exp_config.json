{
  "base_config": "config/fs2.json",
  "model_type": "FastSpeech2",
  "dataset": [
    "LJSpeech"
  ],
  "dataset_path": {
    // TODO: Fill in your dataset path
    "LJSpeech": "[LJSpeech dataset path]"
  },
  // TODO: Fill in the output log path. The default value is "Amphion/ckpts/tts"
  "log_dir": "ckpts/tts",
  "preprocess": {
    // TODO: Fill in the output data path. The default value is "Amphion/data"
    "processed_dir": "data",
    "sample_rate": 22050,
  },
  "train": {
    "batch_size": 16,
    "max_epoch": 100,
  }
}
