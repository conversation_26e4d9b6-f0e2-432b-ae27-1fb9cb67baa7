{
    "model_type": "VQVAE",
    "dataset": {
        "emilia": 1, // 101k hours, 34m samples
        // "emilia": 0.001, // debug
    },
    "preprocess": {
        "hop_size": 320,
        "sample_rate": 16000,
        "max_length": 128000,
        "random_crop": true,
        "processed_dir": "",
        "valid_file": "valid.json",
        "train_file": "train.json"
    },
    "model": {
        "repcodec": {
            "codebook_size": 8192,
            "hidden_size": 1024, // Representations Dim
            "codebook_dim": 8,
            "vocos_dim": 384,
            "vocos_intermediate_dim": 2048,
            "vocos_num_layers": 12
        },
        "representation_type": "hubert",
        "representation_sample_rate": 16000,
        "use_norm_feat": true,
        "representation_stat_mean_var_path": "models/vc/vevo/config/hubert_large_l18_mean_std.npz"
    },
    "log_dir": "ckpts/vevo",
    "train": {
        "max_epoch": 0,
        "use_dynamic_batchsize": false,
        "max_tokens": 16000000,
        "max_sentences": 200,
        "lr_warmup_steps": 10000,
        "lr_scheduler": "constant",
        "num_train_steps": 100000,
        "adam": {
            "lr": 1e-4,
            "betas": [
                0.5,
                0.9
            ]
        },
        "ddp": false,
        "random_seed": 114,
        "batch_size": 32, // use batch_size if not use dynamic batchsize
        "epochs": 5000,
        "max_steps": 1000000,
        "total_training_steps": 800000,
        "save_summary_steps": 500,
        "save_checkpoints_steps": 2000,
        "valid_interval": 2000,
        "keep_checkpoint_max": 100,
        "gradient_accumulation_step": 1,
        "tracker": [
            "tensorboard"
        ],
        "save_checkpoint_stride": [
            1
        ],
        "keep_last": [
            10
        ],
        "run_eval": [
            true
        ],
        "dataloader": {
            "num_worker": 16,
            "pin_memory": true
        },
        "use_emilia_dataset": true
    }
}