{
    "model_type": "FlowMatchingTransformer",
    "dataset": {
        "emilia": 1, // 101k hours, 34m samples
        "singnet": 20 // 400 hours, 0.34m samples * 20 = 6.8m samples
        // // Debug
        // "emilia": 0.001,
        // "singnet": 1
    },
    "singnet_path": "[Please fill out your singing data path]/sing400.json",
    "preprocess": {
        "hop_size": 480,
        "sample_rate": 24000,
        "n_fft": 1920,
        "num_mels": 128,
        "win_size": 1920,
        "fmin": 0,
        "fmax": 12000,
        "mel_var": 8.14,
        "mel_mean": -4.92,
        "f0_fmin": 50.0,
        "f0_fmax": 1100.0,
        "load_phone": false,
        "load_chromagram": true,
        "load_semantic_features": false,
    },
    "model": {
        "flow_matching_transformer": {
            "mel_dim": 128,
            "hidden_size": 1024,
            "num_layers": 16,
            "num_heads": 16,
            "cfg_scale": 0.2,
            "use_cond_code": true, // false means Hidden features
            "cond_codebook_size": 16384, // VQ Codebook Size
            "cond_scale_factor": 4, // 1 means not use ReTrans. 4 means 12.5Hz * 4 = 50Hz. This should be aligned with the frame rate with Mels
            "use_pretrained_model": false,
            "sigma": 1e-5,
            "time_scheduler": "cos",
            "whisper_perturb": false,
        },
        "cond_sample_rate": 16000, // whisper: 16000
        "coco": {
            "coco_type": "content_style", // content, style, or content_style
            "downsample_rate": 4, // The original frame rate is 50 Hz, downsample to 12.5 Hz
            "codebook_size": 16384,
            "hidden_size": 1024, // Representations Dim
            "codebook_dim": 8,
            "encoder": {
                "vocos_dim": 384,
                "vocos_intermediate_dim": 2048,
                "vocos_num_layers": 12,
            },
            "use_normed_whisper": true,
            "whisper_stats_path": "models/svc/vevosing/config/whisper_stats.pt",
            "whisper_dim": 1024,
            "chromagram_dim": 24,
            "pretrained_path": "[Please fill out your pretrained model path]/model.safetensors"
        },
    },
    "log_dir": "ckpts/fm",
    "train": {
        "max_epoch": 0,
        "use_dynamic_batchsize": true,
        "max_tokens": 10000,
        "max_sentences": 50,
        "lr_warmup_steps": 32000,
        "lr_scheduler": "inverse_sqrt",
        "num_train_steps": 800000,
        "adam": {
            "lr": 7.5e-5
        },
        "ddp": false,
        "random_seed": 114,
        "batch_size": 10,
        "epochs": 5000,
        "max_steps": 1000000,
        "total_training_steps": 800000,
        "save_summary_steps": 500,
        "save_checkpoints_steps": 1000,
        "save_checkpoints_backup_steps": 100000,
        "valid_interval": 2000,
        "keep_checkpoint_max": 100,
        "gradient_accumulation_step": 1,
        "resume_skip_steps": true,
        "tracker": [
            "tensorboard"
        ],
        "save_checkpoint_stride": [
            1
        ],
        "keep_last": [
            5
        ],
        "run_eval": [
            true
        ],
        "dataloader": {
            "num_worker": 8,
            "pin_memory": true
        },
        "use_emilia_dataset": true
    }
}