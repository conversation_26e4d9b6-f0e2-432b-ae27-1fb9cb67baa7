{
    "model_type": "AutoregressiveTransformer",
    "dataset": {
        "emilia": 1, // 101k hours, 34m samples
        // "emilia": 0.001, // debug
    },
    "preprocess": {
        "hop_size": 320,
        "sample_rate": 16000, // HuBERT, WavLM, are both 16000
        "n_fft": 1920,
        "num_mels": 128,
        "win_size": 1920,
        "fmin": 0,
        "fmax": 12000,
        "mel_var": 8.14,
        "mel_mean": -4.92,
        "load_phone": true,
    },
    "model": {
        "autoregressive_transformer": {
            "input_vocab_size": 1024, // Eg: 1024 for only G2P-TTS, 32 for content tokens of only VC, 1056 (1024+32) for uni-training of TTS and VC. Note: if vc use random mask, then add one for the random mask token
            "output_vocab_size": 8192,
            "hidden_size": 1920,
            "intermediate_size": 7680,
            "num_hidden_layers": 12,
            "num_attention_heads": 16,
            "use_global_style_encoder": false,
        },
        "train_both_vc_and_tts": false,
        "train_vc": false,
        "train_tts": true,
        "vc_input_token_type": "hubert_codec",
        "vc_input_vocab_size": 32,
        "vc_random_mask_input_prob": -1, // The probability to use random mask for input. -1 means no random mask
        "vc_random_mask_input_max_ratio": 0.2, // The maximum ratio of random mask for input
        "tts_input_token_type": "g2p",
        "tts_input_vocab_size": 1024,
        "output_token_type": "hubert_codec",
        "representation_stat_mean_var_path": "models/vc/vevo/config/hubert_large_l18_mean_std.npz",
        "input_repcodec": {
            "codebook_size": 32,
            "hidden_size": 1024, // Representations Dim
            "codebook_dim": 8,
            "vocos_dim": 384,
            "vocos_intermediate_dim": 2048,
            "vocos_num_layers": 12,
            "pretrained_path": "[Please fill out your pretrained model path]/model.safetensors"
        },
        "output_repcodec": {
            "codebook_size": 8192, // VQ Codebook Size
            "hidden_size": 1024, // Representations Dim
            "codebook_dim": 8,
            "vocos_dim": 384,
            "vocos_intermediate_dim": 2048,
            "vocos_num_layers": 12,
            "pretrained_path": "[Please fill out your pretrained model path]/model.safetensors"
        }
    },
    "log_dir": "ckpts/vevo",
    "train": {
        "max_epoch": 0,
        "use_dynamic_batchsize": true,
        "max_tokens": 5000,
        "max_sentences": 25,
        "lr_warmup_steps": 32000,
        "lr_scheduler": "inverse_sqrt",
        "num_train_steps": 800000,
        "adam": {
            "lr": 2e-4
        },
        "ddp": false,
        "random_seed": 114,
        "batch_size": 10,
        "epochs": 5000,
        "max_steps": 500000,
        "total_training_steps": 500000,
        "save_summary_steps": 500,
        "save_checkpoints_steps": 1000,
        "save_checkpoints_backup_steps": 100000,
        "valid_interval": 2000,
        "keep_checkpoint_max": 100,
        "gradient_accumulation_step": 1,
        "tracker": [
            "tensorboard"
        ],
        "save_checkpoint_stride": [
            1
        ],
        "keep_last": [
            5
        ],
        "run_eval": [
            true
        ],
        "dataloader": {
            "num_worker": 8,
            "pin_memory": true
        },
        "use_emilia_dataset": true
    }
}