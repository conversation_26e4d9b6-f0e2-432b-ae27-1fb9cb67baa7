#!/bin/bash

# Worker Node Setup Script for Distributed Timbre Transfer
# This script copies essential files to the remote worker node

set -e

# Configuration
WORKER_IP="*************"
WORKER_USER="root"  # Change this to your username
WORKER_PATH="/root/ai_compute/Timbre-Transfer"  # Change this to your path
HEAD_IP="*************"

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
print_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
print_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
print_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# Function to copy essential files
copy_files() {
    print_info "📁 Copying essential files to worker node..."
    
    # Create directory on remote server
    ssh ${WORKER_USER}@${WORKER_IP} "mkdir -p ${WORKER_PATH}"
    
    # Copy essential files
    print_info "Copying Ray worker scripts..."
    scp start_ray_worker.py ${WORKER_USER}@${WORKER_IP}:${WORKER_PATH}/
    scp manage_ray_cluster.sh ${WORKER_USER}@${WORKER_IP}:${WORKER_PATH}/
    
    print_info "Copying main service file..."
    scp ray_serve_timbre_only.py ${WORKER_USER}@${WORKER_IP}:${WORKER_PATH}/
    
    print_info "Copying requirements..."
    scp requirements.txt ${WORKER_USER}@${WORKER_IP}:${WORKER_PATH}/
    
    # Copy models directory if it exists
    if [ -d "models" ]; then
        print_info "Copying models directory..."
        scp -r models/ ${WORKER_USER}@${WORKER_IP}:${WORKER_PATH}/
    else
        print_warning "Models directory not found - worker may need to download models"
    fi
    
    # Copy any other Python modules that might be imported
    for file in *.py; do
        if [ "$file" != "start_ray_worker.py" ] && [ "$file" != "ray_serve_timbre_only.py" ]; then
            print_info "Copying $file..."
            scp "$file" ${WORKER_USER}@${WORKER_IP}:${WORKER_PATH}/ 2>/dev/null || true
        fi
    done
    
    print_success "✅ Files copied successfully"
}

# Function to setup environment on worker
setup_environment() {
    print_info "🐍 Setting up Python environment on worker node..."
    
    ssh ${WORKER_USER}@${WORKER_IP} "cd ${WORKER_PATH} && bash -c '
        # Create virtual environment if it doesn't exist
        if [ ! -d \".venv\" ]; then
            python3 -m venv .venv
            echo \"✅ Virtual environment created\"
        fi
        
        # Activate and install dependencies
        source .venv/bin/activate
        pip install --upgrade pip
        pip install -r requirements.txt
        echo \"✅ Dependencies installed\"
        
        # Test GPU access
        python3 -c \"
import torch
print(f\\\"CUDA available: {torch.cuda.is_available()}\\\")
print(f\\\"GPU count: {torch.cuda.device_count()}\\\")
if torch.cuda.is_available():
    for i in range(torch.cuda.device_count()):
        print(f\\\"GPU {i}: {torch.cuda.get_device_name(i)}\\\")
\"
    '"
    
    print_success "✅ Environment setup completed"
}

# Function to start worker
start_worker() {
    print_info "🚀 Starting worker node..."
    
    ssh ${WORKER_USER}@${WORKER_IP} "cd ${WORKER_PATH} && bash -c '
        source .venv/bin/activate
        export CUDA_VISIBLE_DEVICES=1,2,3
        python3 start_ray_worker.py --head-ip ${HEAD_IP} --head-port 10001 --num-gpus 3 --gpu-ids 1,2,3
    '"
}

# Function to show connection command
show_connection_command() {
    print_info "🔗 To manually connect the worker node, run this on ${WORKER_IP}:"
    echo ""
    echo "ssh ${WORKER_USER}@${WORKER_IP}"
    echo "cd ${WORKER_PATH}"
    echo "source .venv/bin/activate"
    echo "export CUDA_VISIBLE_DEVICES=1,2,3"
    echo "python3 start_ray_worker.py --head-ip ${HEAD_IP} --head-port 10001 --num-gpus 3 --gpu-ids 1,2,3"
    echo ""
}

# Function to check connectivity
check_connectivity() {
    print_info "🔍 Checking connectivity to worker node..."
    
    if ping -c 1 ${WORKER_IP} >/dev/null 2>&1; then
        print_success "✅ Worker node ${WORKER_IP} is reachable"
    else
        print_error "❌ Cannot reach worker node ${WORKER_IP}"
        return 1
    fi
    
    if ssh -o ConnectTimeout=5 ${WORKER_USER}@${WORKER_IP} "echo 'SSH connection successful'" 2>/dev/null; then
        print_success "✅ SSH connection to worker node successful"
    else
        print_error "❌ Cannot SSH to worker node ${WORKER_IP}"
        print_info "Make sure SSH keys are set up or use password authentication"
        return 1
    fi
}

# Main menu
show_usage() {
    echo "Worker Node Setup for Distributed Timbre Transfer"
    echo ""
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  check               Check connectivity to worker node"
    echo "  copy                Copy essential files to worker node"
    echo "  setup               Setup Python environment on worker node"
    echo "  start               Start worker node (interactive)"
    echo "  show-command        Show manual connection command"
    echo "  full-setup          Run copy + setup + show command"
    echo "  help                Show this help message"
    echo ""
    echo "Configuration:"
    echo "  Worker IP:          ${WORKER_IP}"
    echo "  Worker User:        ${WORKER_USER}"
    echo "  Worker Path:        ${WORKER_PATH}"
    echo "  Head IP:            ${HEAD_IP}"
    echo ""
    echo "Edit this script to change the configuration."
}

# Main execution
case "${1:-help}" in
    check)
        check_connectivity
        ;;
    copy)
        check_connectivity && copy_files
        ;;
    setup)
        setup_environment
        ;;
    start)
        start_worker
        ;;
    show-command)
        show_connection_command
        ;;
    full-setup)
        check_connectivity && copy_files && setup_environment && show_connection_command
        ;;
    help|--help|-h)
        show_usage
        ;;
    *)
        print_error "Unknown command: $1"
        show_usage
        exit 1
        ;;
esac
