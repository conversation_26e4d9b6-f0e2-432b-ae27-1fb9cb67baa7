#!/usr/bin/env python3
"""
Ray Worker Node Startup Script for Distributed Timbre Transfer

This script starts a Ray worker node that connects to the head node.
The worker contributes its GPU resources to the distributed cluster.

Usage:
    python start_ray_worker.py --head-ip ************* --head-port 10001
    python start_ray_worker.py --head-ip ************* --num-gpus 3
"""

import os
import sys
import argparse
import logging
import torch
import ray
import time
import subprocess

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def check_gpu_availability():
    """Check and display GPU information"""
    if torch.cuda.is_available():
        num_gpus = torch.cuda.device_count()
        logger.info(f"🖥️ Worker node found {num_gpus} GPU(s) available:")
        
        for i in range(num_gpus):
            gpu_name = torch.cuda.get_device_name(i)
            gpu_props = torch.cuda.get_device_properties(i)
            gpu_memory = gpu_props.total_memory / 1e9
            logger.info(f"  GPU {i}: {gpu_name} ({gpu_memory:.1f}GB)")
            
        return num_gpus
    else:
        logger.warning("⚠️ No GPUs available on worker node")
        return 0


def check_head_node_connectivity(head_ip, head_port):
    """Check if head node is reachable"""
    logger.info(f"🔍 Checking connectivity to head node {head_ip}:{head_port}...")
    
    try:
        import socket
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5)
        result = sock.connect_ex((head_ip, head_port))
        sock.close()
        
        if result == 0:
            logger.info("✅ Head node is reachable")
            return True
        else:
            logger.error(f"❌ Cannot connect to head node at {head_ip}:{head_port}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Error checking head node connectivity: {e}")
        return False


def start_ray_worker(head_ip, head_port=10001, num_gpus=3, gpu_ids="1,2,3"):
    """Start Ray worker node"""
    if ray.is_initialized():
        logger.info("ℹ️ Ray already initialized - shutting down first")
        ray.shutdown()
    
    available_gpus = check_gpu_availability()
    
    if num_gpus > available_gpus:
        logger.warning(f"⚠️ Requested {num_gpus} GPUs but only {available_gpus} available")
        num_gpus = available_gpus
    
    # Check head node connectivity
    if not check_head_node_connectivity(head_ip, head_port):
        logger.error("❌ Cannot connect to head node - make sure it's running")
        return False
    
    logger.info(f"🚀 Starting Ray worker node...")
    logger.info(f"   Head node: {head_ip}:{head_port}")
    logger.info(f"   GPUs: {num_gpus} (IDs: {gpu_ids})")
    
    # Set CUDA_VISIBLE_DEVICES to use specific GPUs
    os.environ['CUDA_VISIBLE_DEVICES'] = gpu_ids
    logger.info(f"   CUDA_VISIBLE_DEVICES: {gpu_ids}")
    
    try:
        # Connect to Ray head node
        ray.init(
            address=f"{head_ip}:{head_port}",
            num_gpus=num_gpus,
            num_cpus=os.cpu_count(),
            log_to_driver=True,
            ignore_reinit_error=True,
        )
        
        logger.info("✅ Ray worker node connected successfully")
        logger.info(f"📊 Cluster resources: {ray.cluster_resources()}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Failed to connect to Ray head node: {e}")
        return False


def monitor_worker():
    """Monitor the worker node status"""
    logger.info("👀 Monitoring worker node status...")
    logger.info("   Press Ctrl+C to disconnect worker")
    
    try:
        while True:
            if not ray.is_initialized():
                logger.error("❌ Ray connection lost")
                break
                
            resources = ray.cluster_resources()
            nodes = ray.nodes()
            
            total_gpus = resources.get('GPU', 0)
            total_cpus = resources.get('CPU', 0)
            alive_nodes = len([n for n in nodes if n['Alive']])
            
            logger.info(f"📊 Cluster status: {alive_nodes} nodes, {total_gpus} GPUs, {total_cpus} CPUs")
            
            # Show this worker's contribution
            current_node = None
            for node in nodes:
                if node['Alive'] and node.get('NodeManagerAddress') != ray.nodes()[0]['NodeManagerAddress']:
                    current_node = node
                    break
            
            if current_node:
                node_resources = current_node['Resources']
                node_gpus = node_resources.get('GPU', 0)
                node_cpus = node_resources.get('CPU', 0)
                logger.info(f"🔧 This worker contributes: {node_gpus} GPUs, {node_cpus} CPUs")
            
            time.sleep(30)  # Check every 30 seconds
            
    except KeyboardInterrupt:
        logger.info("🛑 Worker monitoring interrupted by user")


def main():
    """Main function"""
    parser = argparse.ArgumentParser(
        description="Start Ray Worker Node for Distributed Timbre Transfer",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Connect worker to head node
  python start_ray_worker.py --head-ip *************
  
  # Connect with specific port
  python start_ray_worker.py --head-ip ************* --head-port 10001
  
  # Connect with specific GPUs
  python start_ray_worker.py --head-ip ************* --num-gpus 3 --gpu-ids 1,2,3
  
  # Connect with debug logging
  python start_ray_worker.py --head-ip ************* --debug
        """
    )
    
    parser.add_argument(
        "--head-ip",
        type=str,
        required=True,
        help="IP address of the Ray head node"
    )
    
    parser.add_argument(
        "--head-port",
        type=int,
        default=10001,
        help="Port of the Ray head node (default: 10001)"
    )
    
    parser.add_argument(
        "--num-gpus",
        type=int,
        default=3,
        help="Number of GPUs to contribute (default: 3)"
    )
    
    parser.add_argument(
        "--gpu-ids",
        type=str,
        default="1,2,3",
        help="GPU IDs to use (default: 1,2,3)"
    )
    
    parser.add_argument(
        "--debug",
        action="store_true",
        help="Enable debug logging"
    )
    
    args = parser.parse_args()
    
    # Set debug logging if requested
    if args.debug:
        logging.getLogger().setLevel(logging.DEBUG)
        logger.debug("🐛 Debug logging enabled")
    
    # Validate arguments
    if args.head_port < 1 or args.head_port > 65535:
        logger.error("❌ Head port must be between 1 and 65535")
        sys.exit(1)
    
    if args.num_gpus < 0:
        logger.error("❌ Number of GPUs cannot be negative")
        sys.exit(1)
    
    # Validate GPU IDs format
    try:
        gpu_list = [int(x.strip()) for x in args.gpu_ids.split(',')]
        if len(gpu_list) != args.num_gpus:
            logger.warning(f"⚠️ GPU IDs count ({len(gpu_list)}) doesn't match num_gpus ({args.num_gpus})")
    except ValueError:
        logger.error("❌ Invalid GPU IDs format. Use comma-separated integers (e.g., 1,2,3)")
        sys.exit(1)
    
    try:
        # Start Ray worker node
        logger.info("🎵 Starting Ray Worker Node for Distributed Timbre Transfer...")
        
        if not start_ray_worker(
            head_ip=args.head_ip,
            head_port=args.head_port,
            num_gpus=args.num_gpus,
            gpu_ids=args.gpu_ids
        ):
            logger.error("❌ Failed to start Ray worker node")
            sys.exit(1)
        
        # Monitor the worker
        monitor_worker()
        
    except KeyboardInterrupt:
        logger.info("🛑 Worker node interrupted by user")
    except Exception as e:
        logger.error(f"❌ Worker node failed: {e}")
        sys.exit(1)
    finally:
        # Cleanup
        logger.info("🧹 Cleaning up...")
        if ray.is_initialized():
            ray.shutdown()
        logger.info("✅ Cleanup completed")


if __name__ == "__main__":
    main()
