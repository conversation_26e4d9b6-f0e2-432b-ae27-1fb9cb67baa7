# Copyright (c) 2023 Amphion.

# This source code is licensed under the MIT license found in the
# LICENSE file in the root directory of this source tree.

"""
TODO:

This module aims to be an entrance that integrates all the "text" features extraction functions.

The common text features include:
1. phone features that are used for TTS, SVS, etc.

Note:
All the features extraction are designed to utilize GPU to the maximum extent, which can ease the on-the-fly extraction for large-scale dataset.

"""
