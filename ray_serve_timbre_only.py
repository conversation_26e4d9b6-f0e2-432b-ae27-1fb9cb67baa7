import os
import shutil
import torch
import gc
import logging
from typing import Dict, Any
from tempfile import NamedTemporaryFile
import time
import numpy as np
from fastapi.middleware.cors import CORSMiddleware
# FastAPI and Pydantic imports
from fastapi import FastAPI, UploadFile, File, Form, HTTPException
from fastapi.responses import FileResponse

# Ray Serve imports
import ray
from ray import serve
from ray.serve import deployment

# Audio processing imports
from pydub import AudioSegment
from pydub.effects import normalize
import soundfile as sf
from df.enhance import init_df, enhance

# Model imports
from huggingface_hub import snapshot_download
from models.vc.vevo.vevo_utils import VevoInferencePipeline, save_audio

# ===== Logger Setup =====
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)
# ===== Model Paths Setup =====
def setup_model_paths():
    """Download model files and setup paths for timbre transfer only"""
    logger.info("🔧 Setting up timbre transfer model paths...")
    
    tokenizer_dir = snapshot_download(
        repo_id="amphion/Vevo",
        repo_type="model",
        cache_dir="./ckpts/Vevo",
        allow_patterns=["tokenizer/vq8192/*"],
    )
    tokenizer_ckpt_path = os.path.join(tokenizer_dir, "tokenizer/vq8192")

    fmt_dir = snapshot_download(
        repo_id="amphion/Vevo",
        repo_type="model",
        cache_dir="./ckpts/Vevo",
        allow_patterns=["acoustic_modeling/Vq8192ToMels/*"],
    )
    fmt_cfg_path = "./models/vc/vevo/config/Vq8192ToMels.json"
    fmt_ckpt_path = os.path.join(fmt_dir, "acoustic_modeling/Vq8192ToMels")

    vocoder_dir = snapshot_download(
        repo_id="amphion/Vevo",
        repo_type="model",
        cache_dir="./ckpts/Vevo",
        allow_patterns=["acoustic_modeling/Vocoder/*"],
    )
    vocoder_cfg_path = "./models/vc/vevo/config/Vocoder.json"
    vocoder_ckpt_path = os.path.join(vocoder_dir, "acoustic_modeling/Vocoder")

    logger.info("✅ Timbre transfer model paths setup completed")
    return {
        'tokenizer_ckpt_path': tokenizer_ckpt_path,
        'fmt_cfg_path': fmt_cfg_path,
        'fmt_ckpt_path': fmt_ckpt_path,
        'vocoder_cfg_path': vocoder_cfg_path,
        'vocoder_ckpt_path': vocoder_ckpt_path,
    }

# Setup model paths globally
model_paths = setup_model_paths()

# ===== Audio Processing Functions =====
def convert_to_wav(input_path, temp_wav="temp_input.wav"):
    """Convert audio file to WAV format with proper settings"""
    logger.info(f"🔄 Converting audio to WAV format: {input_path} -> {temp_wav}")
    audio = AudioSegment.from_file(input_path)
    audio = audio.set_channels(1).set_frame_rate(48000)
    audio.export(temp_wav, format="wav")
    logger.info(f"✅ Audio converted to WAV successfully: {temp_wav}")
    return temp_wav

def load_wav_tensor(wav_path, sr=48000):
    """Load WAV file as tensor"""
    data, file_sr = sf.read(wav_path)
    if file_sr != sr:
        raise ValueError(f"Sample rate is {file_sr}, expected {sr}")
    tensor = torch.from_numpy(data).float().unsqueeze(0)
    return tensor, sr

def denoise_tensor(input_tensor, model, df_state):
    """Apply noise cancellation to audio tensor"""
    input_tensor = input_tensor.cpu()
    with torch.no_grad():
        output = enhance(model, df_state, input_tensor)
    return output.squeeze(0).numpy()

def apply_noise_cancellation(input_audio_path, output_audio_path):
    """Apply noise cancellation to audio file"""
    logger.info(f"🔊 Starting noise cancellation: {input_audio_path}")
    temp_wav = convert_to_wav(input_audio_path)
    
    # Initialize DeepFilterNet model
    denoise_model, denoise_df_state, _ = init_df()
    
    logger.info("🔧 Loading audio tensor for noise cancellation...")
    tensor, sr = load_wav_tensor(temp_wav, sr=denoise_df_state.sr())
    logger.info("🔧 Applying noise cancellation to audio...")
    enhanced = denoise_tensor(tensor, denoise_model, denoise_df_state)
    sf.write(output_audio_path, enhanced, sr)
    logger.info(f"✅ Noise cancellation completed: {output_audio_path}")
    os.remove(temp_wav)
    return output_audio_path

def normalize_audio(input_audio_path, output_audio_path):
    """Apply audio normalization to audio file"""
    logger.info(f"🔧 Starting audio normalization: {input_audio_path}")
    audio = AudioSegment.from_file(input_audio_path)
    normalized_audio = normalize(audio)
    normalized_audio.export(output_audio_path, format="wav")
    logger.info(f"✅ Audio normalization completed: {output_audio_path}")
    return output_audio_path

# ===== Ray Serve Timbre Transfer Deployment =====
@serve.deployment(
    num_replicas=6,  # 6 replicas total (2 per GPU: 1, 2, 3)
    ray_actor_options={
        "num_gpus": 0.5,  # Half GPU per replica (efficient memory usage)
        "memory": 3 * 1024 * 1024 * 1024,  # 2GB RAM per replica (reduced)
        "num_cpus": 0.5,  # Half CPU per replica to reduce resource pressure
    },
    max_ongoing_requests=3,  # Increased back to 3 with optimizations
    # Enable batching for better throughput
    autoscaling_config=None,  # Disable autoscaling for consistent performance
)
class TimbreTransferDeployment:
    def __init__(self):
        """Initialize the timbre transfer model with GPU allocation"""
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        logger.info(f"🚀 Initializing TimbreTransferDeployment on device: {self.device}")
        
        # Optimize GPU memory settings for 0.5 GPU allocation (~8GB)
        if torch.cuda.is_available():
            # Set memory fraction to use 50% of 16GB (8GB per replica)
            torch.cuda.set_per_process_memory_fraction(0.5)
            torch.cuda.empty_cache()
            gc.collect()

            # Get GPU info
            gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1e9
            logger.info(f"🧹 GPU cache cleared - Available memory: {gpu_memory:.1f}GB")
            logger.info("🔧 GPU memory optimized for 0.5 GPU allocation (~8GB per replica)")

        logger.info("⚡ Optimizations: Reduced flow steps (20) + Mixed precision for faster inference")
        
        # Load the timbre transfer pipeline
        self.pipeline = VevoInferencePipeline(
            content_style_tokenizer_ckpt_path=model_paths['tokenizer_ckpt_path'],
            fmt_cfg_path=model_paths['fmt_cfg_path'],
            fmt_ckpt_path=model_paths['fmt_ckpt_path'],
            vocoder_cfg_path=model_paths['vocoder_cfg_path'],
            vocoder_ckpt_path=model_paths['vocoder_ckpt_path'],
            device=self.device,
        )
        
        logger.info("✅ TimbreTransferDeployment initialized successfully")

        # Warm up the model
        self._warmup()

    def _cleanup_gpu_memory(self):
        """Clean up GPU memory to prevent memory leaks"""
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
            gc.collect()
            # Log current GPU memory usage
            allocated = torch.cuda.memory_allocated() / 1e9
            reserved = torch.cuda.memory_reserved() / 1e9
            logger.info(f"🧹 GPU memory cleaned - Allocated: {allocated:.2f}GB, Reserved: {reserved:.2f}GB")

    def _safe_inference_fm(self, src_wav_path, timbre_ref_wav_path, flow_matching_steps):
        """Wrapper around pipeline.inference_fm with proper memory cleanup"""
        try:
            # Run inference
            result = self.pipeline.inference_fm(
                src_wav_path=src_wav_path,
                timbre_ref_wav_path=timbre_ref_wav_path,
                flow_matching_steps=flow_matching_steps,
            )

            # Force cleanup of any intermediate tensors that might be lingering
            if torch.cuda.is_available():
                torch.cuda.empty_cache()

            return result

        except Exception as e:
            # Clean up on error
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
            raise e

    def _warmup(self):
        """Warm up the model to reduce cold start latency"""
        try:
            logger.info("🔥 Warming up TimbreTransferDeployment...")
            # Create dummy audio files for warmup
            dummy_audio = np.random.randn(24000).astype(np.float32)  # 1 second of audio
            
            with NamedTemporaryFile(suffix=".wav", delete=False) as src_temp, \
                 NamedTemporaryFile(suffix=".wav", delete=False) as ref_temp:
                
                sf.write(src_temp.name, dummy_audio, 24000)
                sf.write(ref_temp.name, dummy_audio, 24000)
                
                # Run a dummy inference for warmup with mixed precision
                with torch.cuda.amp.autocast(enabled=torch.cuda.is_available()):
                    warmup_result = self._safe_inference_fm(
                        src_wav_path=src_temp.name,
                        timbre_ref_wav_path=ref_temp.name,
                        flow_matching_steps=8,  # Reduced steps for warmup
                    )

                # Clean up GPU memory after warmup
                if torch.cuda.is_available():
                    del warmup_result
                    torch.cuda.empty_cache()
                    gc.collect()

                # Clean up
                os.unlink(src_temp.name)
                os.unlink(ref_temp.name)
                
            logger.info("✅ TimbreTransferDeployment warmup completed")
        except Exception as e:
            logger.warning(f"⚠️ Warmup failed: {e}")
    
    async def __call__(self, request_data: dict) -> dict:
        """Handle timbre transfer requests"""
        try:
            start_time = time.time()
            
            # Extract file paths from request
            src_path = request_data["source_path"]
            ref_path = request_data["reference_path"]
            output_path = request_data["output_path"]
            noise_cancellation = request_data.get("noise_cancellation", False)
            normalize_audio_flag = request_data.get("normalize_audio_flag", False)
            flow_matching_steps = request_data.get("flow_matching_steps", 20)  # Optimized: reduced from 32 to 20
            
            logger.info(f"🎵 Processing timbre transfer: {src_path} -> {output_path}")
            
            # Apply preprocessing if requested
            processed_src_path = src_path
            processed_ref_path = ref_path
            temp_files_to_cleanup = []
            
            if noise_cancellation:
                logger.info("🔊 Applying noise cancellation...")
                with NamedTemporaryFile(suffix=".wav", delete=False) as denoised_src, \
                     NamedTemporaryFile(suffix=".wav", delete=False) as denoised_ref:
                    
                    apply_noise_cancellation(src_path, denoised_src.name)
                    apply_noise_cancellation(ref_path, denoised_ref.name)
                    processed_src_path = denoised_src.name
                    processed_ref_path = denoised_ref.name
                    temp_files_to_cleanup.extend([denoised_src.name, denoised_ref.name])
            
            if normalize_audio_flag:
                logger.info("🔧 Applying audio normalization...")
                with NamedTemporaryFile(suffix=".wav", delete=False) as norm_src, \
                     NamedTemporaryFile(suffix=".wav", delete=False) as norm_ref:
                    
                    normalize_audio(processed_src_path, norm_src.name)
                    normalize_audio(processed_ref_path, norm_ref.name)
                    
                    # Clean up previous temp files if they were created
                    if processed_src_path != src_path:
                        os.unlink(processed_src_path)
                        temp_files_to_cleanup.remove(processed_src_path)
                    if processed_ref_path != ref_path:
                        os.unlink(processed_ref_path)
                        temp_files_to_cleanup.remove(processed_ref_path)
                    
                    processed_src_path = norm_src.name
                    processed_ref_path = norm_ref.name
                    temp_files_to_cleanup.extend([norm_src.name, norm_ref.name])
            
            # Run timbre transfer inference with mixed precision and reduced flow steps
            logger.info("🔧 Running timbre transfer inference with mixed precision...")

            # Enable mixed precision optimizations (safe for Ray Serve)
            with torch.cuda.amp.autocast(enabled=torch.cuda.is_available()):
                gen_audio = self._safe_inference_fm(
                    src_wav_path=processed_src_path,
                    timbre_ref_wav_path=processed_ref_path,
                    flow_matching_steps=flow_matching_steps,
                )
            
            # Save output
            save_audio(gen_audio, output_path=output_path)

            # Clean up GPU memory to prevent memory leaks
            if torch.cuda.is_available():
                # Clear variables that might hold GPU tensors
                del gen_audio
            self._cleanup_gpu_memory()

            # Clean up temporary files
            for temp_file in temp_files_to_cleanup:
                try:
                    os.unlink(temp_file)
                except OSError:
                    pass

            end_time = time.time()
            latency = end_time - start_time
            
            logger.info(f"✅ Timbre transfer completed in {latency:.2f} seconds")
            
            return {
                "success": True,
                "latency": latency,
                "message": f"Timbre transfer completed successfully in {latency:.2f}s",
                "output_path": output_path,
                "flow_matching_steps": flow_matching_steps
            }
            
        except Exception as e:
            logger.error(f"❌ Timbre transfer failed: {e}")

            # Clean up GPU memory even on failure to prevent memory leaks
            self._cleanup_gpu_memory()

            return {
                "success": False,
                "latency": 0.0,
                "message": f"Timbre transfer failed: {str(e)}",
                "output_path": None
            }

# ===== FastAPI Application =====
app = FastAPI(title="Ray Serve Timbre Transfer API")
from fastapi.middleware.cors import CORSMiddleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Or specify allowed origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Ray Serve handle
timbre_handle = None

@app.on_event("startup")
async def startup_event():
    """Initialize Ray Serve deployment for timbre transfer"""
    global timbre_handle

    logger.info("🚀 Starting Ray Serve Timbre Transfer deployment...")

    # Initialize Ray if not already initialized
    if not ray.is_initialized():
        ray.init(address="auto", ignore_reinit_error=True)

    # Start Ray Serve with custom HTTP configuration to bind to all interfaces
    serve.start(
        http_options={
            "host": "0.0.0.0",  # Bind to all interfaces (accessible from external IPs)
            "port": 8003,  # Internal Ray Serve port (different from FastAPI)
            "location": "EveryNode"
        },
        detached=False
    )

    # Deploy the timbre transfer model
    timbre_handle = serve.run(
        TimbreTransferDeployment.bind(),
        name="timbre-transfer"
    )

    logger.info("✅ Ray Serve Timbre Transfer deployment started successfully")
    logger.info("📋 Available endpoints:")
    logger.info("  - POST /timbre-transfer/ - Transfer timbre between audio files")
    logger.info("  - GET /health - Health check")
    logger.info("  - GET /stats - GPU and performance statistics")

@app.on_event("shutdown")
async def shutdown_event():
    """Cleanup Ray Serve deployment"""
    logger.info("🛑 Shutting down Ray Serve deployment...")
    serve.shutdown()
    if ray.is_initialized():
        ray.shutdown()
    logger.info("✅ Ray Serve shutdown completed")

@app.post("/timbre-transfer/")
async def timbre_transfer_endpoint(
    source_audio: UploadFile = File(...),
    reference_audio: UploadFile = File(...),
    noise_cancellation: bool = Form(False),
    normalize_audio_flag: bool = Form(False),
    flow_matching_steps: int = Form(20),  # Optimized: reduced from 32 to 20 for faster inference
):
    """Timbre transfer endpoint using Ray Serve with GPU acceleration"""
    logger.info("🚀 Starting timbre transfer request via Ray Serve")
    logger.info(f"📁 Source audio: {source_audio.filename} ({source_audio.content_type})")
    logger.info(f"📁 Reference audio: {reference_audio.filename} ({reference_audio.content_type})")
    logger.info(f"🔊 Noise cancellation: {noise_cancellation}")
    logger.info(f"🔧 Audio normalization: {normalize_audio_flag}")
    logger.info(f"🎯 Flow matching steps: {flow_matching_steps}")

    # Validate flow matching steps
    if flow_matching_steps < 1 or flow_matching_steps > 100:
        raise HTTPException(status_code=400, detail="flow_matching_steps must be between 1 and 100")

    # Save uploaded files to temporary locations
    with NamedTemporaryFile(delete=False, suffix=".wav") as src_temp, \
         NamedTemporaryFile(delete=False, suffix=".wav") as ref_temp, \
         NamedTemporaryFile(delete=False, suffix=".wav") as out_temp:

        logger.info("💾 Saving uploaded files to temporary locations...")
        shutil.copyfileobj(source_audio.file, src_temp)
        shutil.copyfileobj(reference_audio.file, ref_temp)

        src_path = src_temp.name
        ref_path = ref_temp.name
        out_path = out_temp.name

    try:
        # Prepare request data for Ray Serve
        request_data = {
            "source_path": src_path,
            "reference_path": ref_path,
            "output_path": out_path,
            "noise_cancellation": noise_cancellation,
            "normalize_audio_flag": normalize_audio_flag,
            "flow_matching_steps": flow_matching_steps,
        }

        # Call Ray Serve deployment
        result = await timbre_handle.remote(request_data)

        if result["success"]:
            response = FileResponse(
                out_path,
                media_type="audio/wav",
                filename="timbre_transfer_output.wav"
            )
            response.headers["X-Inference-Latency"] = str(result["latency"])
            response.headers["X-Ray-Serve"] = "true"
            response.headers["X-Flow-Matching-Steps"] = str(result["flow_matching_steps"])

            # Clean up source and reference temp files
            try:
                os.remove(src_path)
                os.remove(ref_path)
                logger.info("✅ Temporary input files cleaned up successfully")
            except OSError as e:
                logger.warning(f"⚠️ Could not clean up some temporary files: {e}")

            logger.info("🎉 Timbre transfer request completed successfully via Ray Serve")
            return response
        else:
            raise HTTPException(status_code=500, detail=result["message"])

    except Exception as e:
        # Clean up all temporary files on error
        for temp_path in [src_path, ref_path, out_path]:
            try:
                if os.path.exists(temp_path):
                    os.remove(temp_path)
            except OSError:
                pass

        logger.error(f"❌ Timbre transfer failed: {e}")
        raise HTTPException(status_code=500, detail=f"Timbre transfer failed: {str(e)}")

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    gpu_available = torch.cuda.is_available()
    gpu_count = torch.cuda.device_count() if gpu_available else 0

    health_data = {
        "status": "healthy",
        "ray_initialized": ray.is_initialized(),
        "timbre_deployment": timbre_handle is not None,
        "gpu_available": gpu_available,
        "gpu_count": gpu_count,
    }

    if gpu_available:
        health_data["gpu_memory"] = {
            "allocated_gb": torch.cuda.memory_allocated() / 1e9,
            "reserved_gb": torch.cuda.memory_reserved() / 1e9,
        }

    return health_data

@app.get("/stats")
async def get_stats():
    """Get performance and GPU statistics"""
    if not ray.is_initialized():
        raise HTTPException(status_code=503, detail="Ray not initialized")

    stats = {
        "ray_cluster": {
            "nodes": len(ray.nodes()),
            "resources": ray.cluster_resources(),
        },
        "gpu_stats": {},
        "serve_status": "running" if timbre_handle else "not_initialized",
    }

    if torch.cuda.is_available():
        stats["gpu_stats"] = {
            "device_count": torch.cuda.device_count(),
            "current_device": torch.cuda.current_device(),
            "memory_allocated": torch.cuda.memory_allocated() / 1e9,
            "memory_reserved": torch.cuda.memory_reserved() / 1e9,
            "memory_cached": torch.cuda.memory_cached() / 1e9,
        }

        for i in range(torch.cuda.device_count()):
            device_props = torch.cuda.get_device_properties(i)
            stats["gpu_stats"][f"gpu_{i}"] = {
                "name": device_props.name,
                "total_memory_gb": device_props.total_memory / 1e9,
                "multi_processor_count": device_props.multi_processor_count,
            }

    return stats

@app.post("/cleanup-memory")
async def cleanup_memory():
    """Force GPU memory cleanup to prevent memory leaks"""
    if not torch.cuda.is_available():
        return {"message": "CUDA not available", "success": False}

    # Get memory stats before cleanup
    before_allocated = torch.cuda.memory_allocated() / 1e9
    before_reserved = torch.cuda.memory_reserved() / 1e9

    # Force cleanup
    torch.cuda.empty_cache()
    gc.collect()

    # Get memory stats after cleanup
    after_allocated = torch.cuda.memory_allocated() / 1e9
    after_reserved = torch.cuda.memory_reserved() / 1e9

    return {
        "success": True,
        "message": "GPU memory cleanup completed",
        "memory_stats": {
            "before": {
                "allocated_gb": before_allocated,
                "reserved_gb": before_reserved
            },
            "after": {
                "allocated_gb": after_allocated,
                "reserved_gb": after_reserved
            },
            "freed": {
                "allocated_gb": before_allocated - after_allocated,
                "reserved_gb": before_reserved - after_reserved
            }
        }
    }

@app.get("/")
async def root():
    """Root endpoint with API information"""
    return {
        "message": "Ray Serve Timbre Transfer API",
        "version": "1.0.0",
        "description": "GPU-accelerated timbre transfer using Ray Serve",
        "endpoints": [
            "POST /timbre-transfer/ - Transfer timbre between audio files",
            "GET /health - Health check and system status",
            "GET /stats - Detailed performance and GPU statistics",
        ],
        "features": [
            "GPU acceleration with Ray Serve",
            "Multiple replicas for better throughput",
            "Noise cancellation support",
            "Audio normalization support",
            "Configurable flow matching steps",
        ],
        "ray_serve": True
    }
