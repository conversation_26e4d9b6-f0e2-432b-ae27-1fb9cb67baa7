#!/usr/bin/env python3
"""
Simple deployment script for Ray Serve Timbre Transfer API

Usage:
    python start_timbre_server.py --port 8009 --gpu-check
    python start_timbre_server.py --host 0.0.0.0 --port 8009
"""

import os
import sys
import argparse
import logging
import torch
import ray
from ray import serve
import uvicorn

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def check_gpu_availability():
    """Check and display GPU information"""
    if torch.cuda.is_available():
        num_gpus = torch.cuda.device_count()
        logger.info(f"🖥️ Found {num_gpus} GPU(s) available:")
        
        for i in range(num_gpus):
            gpu_name = torch.cuda.get_device_name(i)
            gpu_props = torch.cuda.get_device_properties(i)
            gpu_memory = gpu_props.total_memory / 1e9
            logger.info(f"  GPU {i}: {gpu_name} ({gpu_memory:.1f}GB)")
            
        return num_gpus
    else:
        logger.warning("⚠️ No GPUs available - will run on CPU (much slower)")
        return 0


def initialize_ray(num_gpus=None):
    """Initialize Ray with GPU support"""
    if ray.is_initialized():
        logger.info("ℹ️ Ray already initialized")
        return
    
    available_gpus = check_gpu_availability()
    
    if num_gpus is None:
        num_gpus = available_gpus
    elif num_gpus > available_gpus:
        logger.warning(f"⚠️ Requested {num_gpus} GPUs but only {available_gpus} available")
        num_gpus = available_gpus
    
    logger.info(f"🚀 Initializing Ray with {num_gpus} GPUs...")
    
    ray.init(
        num_gpus=num_gpus,
        num_cpus=os.cpu_count(),
        ignore_reinit_error=True,
        log_to_driver=True,
    )
    
    logger.info("✅ Ray initialized successfully")
    logger.info(f"📊 Ray cluster resources: {ray.cluster_resources()}")


def start_server(host="0.0.0.0", port=8009, num_gpus=None, workers=1):
    """Start the Ray Serve timbre transfer server"""
    try:
        # Initialize Ray
        initialize_ray(num_gpus)
        
        # Import and start the FastAPI app
        from ray_serve_timbre_only import app
        
        logger.info(f"🌐 Starting Timbre Transfer API server on {host}:{port}")
        logger.info("📋 Available endpoints:")
        logger.info(f"  - http://{host}:{port}/timbre-transfer/ (POST)")
        logger.info(f"  - http://{host}:{port}/health (GET)")
        logger.info(f"  - http://{host}:{port}/stats (GET)")
        logger.info(f"  - http://{host}:{port}/ (GET)")
        
        # Start the server
        uvicorn.run(
            app,
            host=host,
            port=port,
            workers=workers,
            log_level="info",
            access_log=True,
        )
        
    except KeyboardInterrupt:
        logger.info("🛑 Server interrupted by user")
    except Exception as e:
        logger.error(f"❌ Server failed to start: {e}")
        sys.exit(1)
    finally:
        # Cleanup
        logger.info("🧹 Cleaning up...")
        if ray.is_initialized():
            serve.shutdown()
            ray.shutdown()
        logger.info("✅ Cleanup completed")


def main():
    """Main function"""
    parser = argparse.ArgumentParser(
        description="Start Ray Serve Timbre Transfer API",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Check GPU availability
  python start_timbre_server.py --gpu-check
  
  # Start server with default settings
  python start_timbre_server.py
  
  # Start server on specific host/port
  python start_timbre_server.py --host 0.0.0.0 --port 8009
  
  # Start server with specific number of GPUs
  python start_timbre_server.py --num-gpus 1
  
  # Start server with multiple workers (experimental)
  python start_timbre_server.py --workers 2
        """
    )
    
    parser.add_argument(
        "--host",
        type=str,
        default="0.0.0.0",
        help="Host to bind the server to (default: 0.0.0.0)"
    )
    
    parser.add_argument(
        "--port",
        type=int,
        default=8010,
        help="Port to bind the server to (default: 8010)"
    )
    
    parser.add_argument(
        "--num-gpus",
        type=int,
        default=None,
        help="Number of GPUs to use (default: all available)"
    )
    
    parser.add_argument(
        "--workers",
        type=int,
        default=1,
        help="Number of worker processes (default: 1)"
    )
    
    parser.add_argument(
        "--gpu-check",
        action="store_true",
        help="Check GPU availability and exit"
    )
    
    parser.add_argument(
        "--debug",
        action="store_true",
        help="Enable debug logging"
    )
    
    args = parser.parse_args()
    
    # Set debug logging if requested
    if args.debug:
        logging.getLogger().setLevel(logging.DEBUG)
        logger.debug("🐛 Debug logging enabled")
    
    # GPU check mode
    if args.gpu_check:
        num_gpus = check_gpu_availability()
        if num_gpus > 0:
            logger.info("✅ GPU check completed - GPUs are available")
            sys.exit(0)
        else:
            logger.warning("⚠️ GPU check completed - no GPUs available")
            sys.exit(1)
    
    # Validate arguments
    if args.port < 1 or args.port > 65535:
        logger.error("❌ Port must be between 1 and 65535")
        sys.exit(1)
    
    if args.workers < 1:
        logger.error("❌ Number of workers must be at least 1")
        sys.exit(1)
    
    if args.num_gpus is not None and args.num_gpus < 0:
        logger.error("❌ Number of GPUs cannot be negative")
        sys.exit(1)
    
    # Start the server
    logger.info("🎵 Starting Ray Serve Timbre Transfer API...")
    start_server(
        host=args.host,
        port=args.port,
        num_gpus=args.num_gpus,
        workers=args.workers
    )


if __name__ == "__main__":
    main()
